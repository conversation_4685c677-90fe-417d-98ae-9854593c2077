html, body {
  height: 0;
}

body {
  border: 0;
  margin: 0;
  padding: 0;
  width: 500px;
}

.content {
  border: 0;
  margin: 0;
  padding: 0;
  width: 100%;
}

.content table {
  width: 100%;
  margin: auto;
  border-spacing: 0;
}

.content > table {
  border-spacing: 0 10px;
  border-bottom: solid 1px rgba(0,0,0,0.1);
}

.content table tr td {
  color: #555;
  font-size: 12px;
  font-family: arial,sans-serif;
}

.content .logo {
  height: 110px;
  background: url("../icons/128.png") no-repeat center center;
  background-size: 64px;
}

.content .name {
  color: #777;
  user-select: none;
  text-align: center;
}

.content .buttons {
  height: 150px;
  border-left: solid 1px rgba(0,0,0,0.1);
}

.content .buttons .icon {
  width: 32px;
  font-size: 17px;
  cursor: pointer;
  line-height: 37px;
  user-select: none;
  text-align: center;
  font-family: monospace;
}

.content .buttons .icon svg {
  fill: #777;
  margin: -5px 0 0 0;
  vertical-align: middle;
}

.content .buttons .button {
  padding: 0;
  cursor: pointer;
  text-indent: 5px;
  user-select: none;
}

.content .buttons tr {
  transition: 300ms ease all;
}

.content .buttons tr:hover {
  background-color: rgba(0,0,0,0.1);
}

@-moz-document url-prefix() {
  html, body {
    height: auto;
  }
}