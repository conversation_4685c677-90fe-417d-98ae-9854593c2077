from library import *
class TTC:
    def __init__(self, username, password) -> None:
        self.username, self.password = username, password
        self.proxyDict               = ''
        self.API_URL                 = 'https://tuongtaccheo.com/'
        
    def addProxy(self, proxy: str):
        try:
            if len(proxy.split(':')) >= 4:
                parts = proxy.split(':')
                iport = ":".join(parts[:2])
                userpass = ":".join(parts[2:])
                self.proxyDict = {
                    'https': 'http://{}@{}'.format(userpass,iport),
                    'http': 'http://{}@{}'.format(userpass,userpass)
                    }
            elif len(proxy.split(':')) >= 2:
                proxy_url = 'http://{}'.format(proxy)
                self.proxyDict   = { 
                "http"  : proxy_url, 
                "https" : proxy_url
            }
            else:
                self.proxyDict   = ''

            ip = requests.get('https://www.myip.com/', proxies=self.proxyDict).text
            # print('My ip address:',ip.split('<span id="ip">')[1].split('</span>')[0],self.username,end='\r')
            return True
        except: return False
    def __requests(self, endpoint: str, data = None):
        for x in range(3):
            try:
          
                if data  == None:self.lastReponse = requests.get(self.API_URL + endpoint, headers = self.headers, proxies = self.proxyDict, timeout = 15)
                else:self.lastReponse = requests.post(self.API_URL + endpoint, data = data, headers = self.headers, timeout = 15)
                return True
               
            except:
                pass
            time.sleep(1)
    
    def loginTTC(self):
        try:
            rsp           = requests.get('https://tuongtaccheo.com/', proxies=self.proxyDict)
            cookie        =  rsp.headers['Set-cookie']
            self.headers  = {
                'Content-type': "application/x-www-form-urlencoded",
                "x-requested-with":"XMLHttpRequest",
                "Cookie":cookie,
            }
            if self.__requests('login.php', data={'username': self.username,'password': self.password, 'submit': 'ĐĂNG NHẬP',}):
                if self.__requests('home.php'):
                    if 'soduchinh' in self.lastReponse.text:
                        return {'status': "success", 'data': {'user': self.username, 'xu': self.lastReponse.text.split('id="soduchinh">')[1].split("</strong>")[0]}}
                    else:
                        return {'status': "error", 'mess': "Sai tài khoản hoặc mật khẩu"}
        except:pass
        return {'status': "error", 'mess': "Sai tài khoản hoặc mật khẩu"}
    
    def getXuTTC(self):
        try:
            if self.__requests('home.php'):
                return {'user': self.username, 'xu': self.lastReponse.text.split('id="soduchinh">')[1].split("</strong>")[0]}
        except: return {'user':self.username, 'xu': 0}
        
    def getMaLucTTC(self):
        if self.__requests('caidat'):
            soup = BeautifulSoup(self.lastReponse.text, 'html.parser')
            self.username = soup.find('tr', class_='text-center').find('td').text
            self.xuTTC = soup.find_all('tr', class_='text-center')[1].find('td').text
            self.maluc = soup.find_all('tr', class_='text-center')[2].find('td').text
            if 'soduchinh' in self.lastReponse.text:
                return {'user': self.username, 'xu': self.xuTTC, 'maluc': self.maluc}
            else: return False
        return False
        
    def datNik(self, username: str):
        try:
            if self.__requests('cauhinh/addtiktok.php?link={}&nickchay={}'.format(username,username)):
                if '1' in self.lastReponse.text:
                    return {'status': "success", 'mess': "Đặt nick thành công"}
                time.sleep(3)
            return {'status': "error", 'mess': self.lastReponse.text}
        except: return {'status': 'error', 'mess': 'Có lỗi xảy ra khi đặt nick !'}
        
    def getJob(self,type):
        try:
            if type == 'love':
                if self.__requests('tiktok/kiemtien/getpost.php'):
                    if '[]' in self.lastReponse.text:
                        return 0
                    return self.lastReponse.json()
            elif type == 'follow':
                if self.__requests('tiktok/kiemtien/subcheo/getpost.php'):
                    if '[]' in self.lastReponse.text:
                        return 0
                    return self.lastReponse.json()

            elif type == 'cmt':
                if self.__requests('tiktok/kiemtien/cmtcheo/getpost.php'):
                    if '[]' in self.lastReponse.text:
                        return 0
                    return self.lastReponse.json()
        
        except Exception as e:
            logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
            logging.exception("Exception occurred")
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi lấy nhiệm vụ!'}
  
    def getXuJob(self, type: str, id: str):
        try:
            if type == 'follow':
                if self.__requests('tiktok/kiemtien/subcheo/nhantien.php', data={'id': id}):
                    print(self.lastReponse.json(),self.username,id)
                    if 'mess' in self.lastReponse.text and "cộng" in self.lastReponse.text:
                        return {'status': 'success', 'mess': str(self.lastReponse.json()['mess']).split('cộng ')[1].split(' xu')[0]}
                    else:
                        if 'error2' in self.lastReponse.text:
                            return {'status': 'error', 'mess': self.lastReponse.json()['error2']}
                        if 'error' in self.lastReponse.text:
                            
                            return {'status': 'error', 'mess': self.lastReponse.json()['error']}
                        else:
                            return {'status': 'error', 'mess': self.lastReponse.json()['mess']}
            elif type == 'love':
                if self.__requests('tiktok/kiemtien/nhantien.php', data={'id': id}):
                    print(self.lastReponse.json(),self.username)
                    if 'mess' in self.lastReponse.text and "cộng" in self.lastReponse.text:
                        return {'status': 'success', 'mess': str(self.lastReponse.json()['mess']).split('cộng ')[1].split(' xu')[0]}
                    else:
                        if 'error2' in self.lastReponse.text:
                            return {'status': 'error', 'mess': self.lastReponse.json()['error2']}
                        if 'error' in self.lastReponse.text:
                            
                            return {'status': 'error', 'mess': self.lastReponse.json()['error']}
                        else:
                            return {'status': 'error', 'mess': self.lastReponse.json()['mess']}
             
        except Exception as e:
            logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
            logging.exception("Exception occurred")
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi nhận xu!'}

    def chuyenXu(self, usernhan, sluong):
        try:
            data = {
                'usernhan': usernhan,
                'passnicktang': self.password,
                'sluong': sluong,
                'loai': 'xu',
            }

            response = requests.post('https://tuongtaccheo.com/caidat/tangxu.php', headers=self.headers, data=data).text
            if '4' in response:
                xuconlai = self.getXuTTC()['xu']
                return {'status': 'success', 'mess': 'chuyển xu thành công.','xuconlai':xuconlai}
            else: return {'status': 'error', 'mess': 'chuyển xu thất bại.'}
        except: return {'status': 'error', 'mess': 'chuyển xu thất bại.'}