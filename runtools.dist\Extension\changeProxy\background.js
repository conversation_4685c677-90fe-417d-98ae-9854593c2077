function resetProxyToDefault() {
    chrome.proxy.settings.clear({scope: 'regular'}, function() {
        if (chrome.runtime.lastError) {
            console.error("C<PERSON> lỗi khi xóa cài đặt proxy: ", chrome.runtime.lastError);
        } else {
            console.log("Proxy đã được đặt lại về mặc định.");
        }
    });
}

chrome.windows.onRemoved.addListener(function() {
    chrome.windows.getAll({}, function(windows) {
        if (windows.length === 0) {
            resetProxyToDefault();
        }
    });
});

chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    console.log(changeInfo);
    if (changeInfo.title && changeInfo.title.includes('PYTOURNES:PROXY_OFF')) {
        resetProxyToDefault();
    }
    if (changeInfo.title && changeInfo.title.includes('PYTOURNES:PROXY_ON')) {
        var titleParts = changeInfo.title.split('PYTOURNES:PROXY_ON:')[1];
        var proxyParts = titleParts.split(":");
        var proxy = proxyParts[0];
        var port = proxyParts[1];
        var user = proxyParts[2];
        var password = proxyParts[3];

        var config = {
            mode: "fixed_servers",
            rules: {
                singleProxy: {
                    scheme: "http",
                    host: proxy,
                    port: parseInt(port)
                },
                bypassList: ["localhost"]
            }
        };
        chrome.proxy.settings.set({value: config, scope: "regular"}, function() {});

        function callbackFn(details) {
            if (user && password) {
                return {
                    authCredentials: {
                        username: user,
                        password: password
                    }
                };
            } else {
                return {}; // Trả về đối tượng rỗng nếu không có username và password
            }
        }
    
        chrome.webRequest.onAuthRequired.removeListener(callbackFn);
        chrome.webRequest.onAuthRequired.addListener(
            callbackFn,
            {urls: ["<all_urls>"]},
            ['blocking']
        );
        
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            var currentTab = tabs[0];
            if (currentTab) {
                chrome.tabs.update(currentTab.id, {url: 'https://ipscore.io/'});
            }
        });
    }
});
