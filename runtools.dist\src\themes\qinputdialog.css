QInputDialog {
    background-color: #2c313c;
    color:#fff;
}
QInputDialog QLabel {
    font-size: 10pt;
    color: #fff;
}
QLineEdit {
    background-color: #1b1e23;
    border-radius: 5px;
    border: 2px solid rgb(33, 37, 43);
    padding-left: 10px;
    height:25px;
    selection-color: rgb(255, 255, 255);
    selection-background-color: rgb(255, 121, 198);
}

QLineEdit:hover {
    color: #fff;
    border: 2px solid rgb(64, 71, 88);
}

QLineEdit:focus {
    color: #fff;
    border: 2px solid rgb(91, 101, 124);
}

QComboBox {
    background-color: rgb(27, 29, 35);
    border-radius: 5px;
    border: 2px solid rgb(33, 37, 43);
    padding: 5px;
    padding-left: 10px;
    color: #2c313c;

}

QComboBox:hover {
    border: 2px solid rgb(64, 71, 88);
    color: #fff;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 35px;
    border-left-width: 3px;
    border-left-color: rgba(39, 44, 54, 150);
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    background-image: url(:/icon/images/icons/cil-arrow-bottom.png);
    background-position: center;
    background-repeat: no-reperat;
}

QComboBox QAbstractItemView {
    color: #fff;
    background-color: rgb(33, 37, 43);
    padding: 10px;
    selection-background-color: rgb(39, 44, 54);
}

QInputDialog QPushButton {
    background-color: #1e2229;
    color: #fff;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
}
QInputDialog QPushButton:hover {
    background-color: #1b1e23;
}

QScrollBar:horizontal {
    border: none;
    background: rgb(52, 59, 72);
    height: 8px;
    margin: 0px 21px 0 21px;
    border-radius: 0px;
}

QScrollBar::handle:horizontal {
    background: rgb(189, 147, 249);
    min-width: 25px;
    border-radius: 4px
}

QScrollBar::add-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}

QScrollBar::up-arrow:horizontal,
QScrollBar::down-arrow:horizontal {
    background: none;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

QScrollBar:vertical {
    border: none;
    background: rgb(52, 59, 72);
    width: 8px;
    margin: 21px 0 21px 0;
    border-radius: 0px;
}

QScrollBar::handle:vertical {
    background: rgb(189, 147, 249);
    min-height: 25px;
    border-radius: 4px
}

QScrollBar::add-line:vertical {
    border: none;
    background: rgb(55, 63, 77);
    height: 20px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    subcontrol-position: bottom;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:vertical {
    border: none;
    background: rgb(55, 63, 77);
    height: 20px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    subcontrol-position: top;
    subcontrol-origin: margin;
}

QScrollBar::up-arrow:vertical,
QScrollBar::down-arrow:vertical {
    background: none;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}
