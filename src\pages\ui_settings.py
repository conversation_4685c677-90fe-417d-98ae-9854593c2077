from Core import *
from src.modules import *
from src.widgets.py_toggle import PyToggle
from functools import partial


class Ui_Settings(object):
    gripSize = 16; grips = []; _old_pos = None
    def setupUi(self, Settings):
        Settings.setObjectName("Settings")
        Settings.resize(1261, 709)
        Settings.setAttribute(Qt.WA_TranslucentBackground)
        Settings.setWindowFlags(Qt.FramelessWindowHint)
        self.stylesheet = QtWidgets.QWidget(Settings)
        self.stylesheet.setStyleSheet("* {\n"
"    background-color: transparent;\n"
"    background: none;\n"
"    border: none;\n"
"    padding: 0;\n"
"    margin: 0;\n"
"    font: 10pt \"Segoe UI\";\n"
"    background: no-repeat;\n"
"    color: #fff;\n"
"}\n"
"\n"
"QWidget {\n"
"    color: rgb(221, 221, 221);\n"
"    font: 10pt \"Segoe UI\";\n"
"}\n"
"\n"
"#MainPages {\n"
"    border-radius: 5px;\n"
"    background-color: #2c313c;\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QTableWidget */\n"
"QTableWidget {\n"
"    background-color: #343b48;\n"
"    color: white;\n"
"    padding: 5;\n"
"    border: none;\n"
"\n"
"}\n"
"\n"
"QTableWidget::item {\n"
"    background-color: rgba(68, 119, 170, 125);\n"
"}\n"
"\n"
"QHeaderView {\n"
"    border-top-left-radius: 5px;\n"
"    border-top-right-radius: 5px;\n"
"    background-color: #1b1e23;\n"
"    padding: 5px 5px;\n"
"    color: white;\n"
"\n"
"    font-family: \'Montserrat\', sans-serif;\n"
"\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color: #1b1e23;\n"
"    border-style: none;\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"LineEdit */\n"
"QLineEdit {\n"
"    background-color: #1b1e23;\n"
"    border-radius: 5px;\n"
"    border: 2px solid rgb(33, 37, 43);\n"
"    padding-left: 10px;\n"
"    selection-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QLineEdit:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"\n"
"QLineEdit:focus {\n"
"    border: 2px solid rgb(91, 101, 124);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"PlainTextEdit */\n"
"QPlainTextEdit {\n"
"    background-color: rgb(27, 29, 35);\n"
"    border-radius: 5px;\n"
"    padding: 10px;\n"
"    selection-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QPlainTextEdit QScrollBar:vertical {\n"
"    width: 8px;\n"
"}\n"
"\n"
"QPlainTextEdit QScrollBar:horizontal {\n"
"    height: 8px;\n"
"}\n"
"\n"
"QPlainTextEdit:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"\n"
"QPlainTextEdit:focus {\n"
"    border: 2px solid rgb(91, 101, 124);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"ScrollBars */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    background: rgb(52, 59, 72);\n"
"    height: 8px;\n"
"    margin: 0px 21px 0 21px;\n"
"    border-radius: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background: rgb(189, 147, 249);\n"
"    min-width: 25px;\n"
"    border-radius: 4px\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    width: 20px;\n"
"    border-top-right-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    subcontrol-position: right;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::sub-line:horizontal {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    width: 20px;\n"
"    border-top-left-radius: 4px;\n"
"    border-bottom-left-radius: 4px;\n"
"    subcontrol-position: left;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::up-arrow:horizontal,\n"
"QScrollBar::down-arrow:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    background: rgb(52, 59, 72);\n"
"    width: 8px;\n"
"    margin: 21px 0 21px 0;\n"
"    border-radius: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background: rgb(189, 147, 249);\n"
"    min-height: 25px;\n"
"    border-radius: 4px\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    height: 20px;\n"
"    border-bottom-left-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    subcontrol-position: bottom;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::sub-line:vertical {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    height: 20px;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    subcontrol-position: top;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"\n"
"QScrollBar::up-arrow:vertical,\n"
"QScrollBar::down-arrow:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"CheckBox */\n"
"QCheckBox::indicator {\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"    width: 15px;\n"
"    height: 15px;\n"
"    border-radius: 5px;\n"
"    background: rgb(44, 49, 60);\n"
"}\n"
"\n"
"QCheckBox::indicator:hover {\n"
"    border: 3px solid rgb(58, 66, 81);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    background: 3px solid rgb(52, 59, 72);\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"    background-image: url(:/icon/images/icons/cil-check-alt.png);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"RadioButton */\n"
"QRadioButton::indicator {\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"    width: 15px;\n"
"    height: 15px;\n"
"    border-radius: 10px;\n"
"    background: rgb(44, 49, 60);\n"
"}\n"
"\n"
"QRadioButton::indicator:hover {\n"
"    border: 3px solid rgb(58, 66, 81);\n"
"}\n"
"\n"
"QRadioButton::indicator:checked {\n"
"    background: 3px solid rgb(94, 106, 130);\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"ComboBox */\n"
"QComboBox {\n"
"    border-radius: 5px;\n"
"    border: 2px solid rgb(33, 37, 43);\n"
"    padding: 3px;\n"
"    padding-left: 10px;\n"
"  }\n"
"  QComboBox:hover {\n"
"    border: 2px solid rgb(64, 71, 88);\n"
"  }\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: padding;\n"
"    subcontrol-position: top right;\n"
"    width: 25px;\n"
"    border-left-width: 3px;\n"
"    border-left-color: rgba(39, 44, 54, 150);\n"
"    border-left-style: solid;\n"
"    border-top-right-radius: 3px;\n"
"    border-bottom-right-radius: 3px;\n"
"    background-image: url(:/icon/images/icons/branch_open.png);\n"
"    background-position: center;\n"
"    background-repeat: no-reperat;\n"
"}\n"
"  \n"
"  QComboBox QAbstractItemView {\n"
"    color: rgb(255, 121, 198);\n"
"   background-color: rgb(33, 37, 43);\n"
"    padding: 10px;\n"
"    selection-background-color: rgb(32, 34, 36);\n"
"  }\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Sliders */\n"
"QSlider::groove:horizontal {\n"
"    border-radius: 5px;\n"
"    height: 10px;\n"
"    margin: 0px;\n"
"    background-color: rgb(52, 59, 72);\n"
"}\n"
"\n"
"QSlider::groove:horizontal:hover {\n"
"    background-color: rgb(55, 62, 76);\n"
"}\n"
"\n"
"QSlider::handle:horizontal {\n"
"    background-color: rgb(189, 147, 249);\n"
"    border: none;\n"
"    height: 10px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"QSlider::handle:horizontal:hover {\n"
"    background-color: rgb(195, 155, 255);\n"
"}\n"
"\n"
"QSlider::handle:horizontal:pressed {\n"
"    background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QSlider::groove:vertical {\n"
"    border-radius: 5px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"    background-color: rgb(52, 59, 72);\n"
"}\n"
"\n"
"QSlider::groove:vertical:hover {\n"
"    background-color: rgb(55, 62, 76);\n"
"}\n"
"\n"
"QSlider::handle:vertical {\n"
"    background-color: rgb(189, 147, 249);\n"
"    border: none;\n"
"    height: 10px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"QSlider::handle:vertical:hover {\n"
"    background-color: rgb(195, 155, 255);\n"
"}\n"
"\n"
"QSlider::handle:vertical:pressed {\n"
"    background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"CommandLinkButton */\n"
"QCommandLinkButton {\n"
"    color: rgb(255, 121, 198);\n"
"    border-radius: 5px;\n"
"    padding: 5px;\n"
"    color: rgb(255, 170, 255);\n"
"}\n"
"\n"
"QCommandLinkButton:hover {\n"
"    color: rgb(255, 170, 255);\n"
"    background-color: rgb(44, 49, 60);\n"
"}\n"
"\n"
"QCommandLinkButton:pressed {\n"
"    color: rgb(189, 147, 249);\n"
"    background-color: rgb(52, 58, 71);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QGroupBox */\n"
"\n"
"QGroupBox {\n"
"    border: 1px solid rgb(221, 221, 221);\n"
"}\n"
"\n"
"QGroupBox {\n"
"    border: 1px solid #343b48;\n"
"    margin-top: 8px;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 4px;\n"
"    padding: 0px 5px 0px 5px;\n"
"}\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QSpinBox */\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QTabWidget */\n"
"\n"
"QTabWidget::pane {\n"
"    border: 1px solid #272c36;\n"
"    background: rgb(245, 245, 245);\n"
"    right:1px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    text-align: left;\n"
"    background: #343b48;\n"
"    border: 1px solid #272c36;\n"
"    padding: 5 3;\n"
"    width: 115;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background: #2c313c;\n"
"    margin-bottom: -1px;\n"
"}\n"
"\n"
"\n"
"QSpinBox {\n"
"    background-color: #1b1e23;\n"
"}\n"
"\n"
"QAbstractSpinBox {\n"
"  background-color: rgb(34, 38, 47);\n"
"  color: #E0E1E3;\n"
"  /* This fixes 103, 111 */\n"
"  padding-top: 2px;\n"
"  /* This fixes 103, 111 */\n"
"  padding-bottom: 2px;\n"
"  padding-left: 4px;\n"
"  padding-right: 4px;\n"
"  border-radius: 4px;\n"
"  /* min-width: 5px; removed to fix 109 */\n"
"}\n"
"\n"
"QAbstractSpinBox:up-button {\n"
"  background-color: transparent #19232D;\n"
"  subcontrol-origin: border;\n"
"  subcontrol-position: top right;\n"
"  border-top-left-radius: 0;\n"
"  border-bottom-left-radius: 0;\n"
"  margin: 1px;\n"
"  width: 12px;\n"
"  margin-bottom: -1px;\n"
"  padding-right:2px;\n"
"}\n"
"\n"
"QAbstractSpinBox::up-arrow, QAbstractSpinBox::up-arrow:disabled, QAbstractSpinBox::up-arrow:off {\n"
"  image: url(:/icon/images/icons/arrow_up.png);\n"
"  height: 8px;\n"
"  width: 8px;\n"
"}\n"
"\n"
"QAbstractSpinBox::up-arrow:hover {\n"
"  image:  url(:/icon/images/icons/arrow_up2x.png);\n"
"}\n"
"\n"
"QAbstractSpinBox:down-button {\n"
"  background-color: transparent #19232D;\n"
"  subcontrol-origin: border;\n"
"  subcontrol-position: bottom right;\n"
"  border-top-left-radius: 0;\n"
"  border-bottom-left-radius: 0;\n"
"  margin: 1px;\n"
"  width: 12px;\n"
"  margin-top: -1px;\n"
"  padding-right:2px;\n"
"}\n"
"\n"
"QAbstractSpinBox::down-arrow, QAbstractSpinBox::down-arrow:disabled, QAbstractSpinBox::down-arrow:off {\n"
"  image:  url(:/icon/images/icons/arrow_down.png);\n"
"  height: 8px;\n"
"  width: 8px;\n"
"}\n"
"\n"
"QAbstractSpinBox::down-arrow:hover {\n"
"  image:  url(:/icon/images/icons/arrow_down2x.png);\n"
"}\n"
"\n"
"QAbstractSpinBox:hover {\n"
"  border: 1px solid #346792;\n"
"  color: #E0E1E3;\n"
"}\n"
"\n"
"QAbstractSpinBox:focus {\n"
"  border: 1px solid #1A72BB;\n"
"}\n"
"\n"
"QAbstractSpinBox:selected {\n"
"  background: #346792;\n"
"  color: #455364;\n"
"}\n"
"")
        self.stylesheet.setObjectName("stylesheet")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.stylesheet)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setSpacing(0)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.MainPages = QtWidgets.QFrame(self.stylesheet)
        self.MainPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.MainPages.setFrameShadow(QtWidgets.QFrame.Raised)
        self.MainPages.setObjectName("MainPages")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.MainPages)
        self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame = QtWidgets.QFrame(self.MainPages)
        self.frame.setStyleSheet("background-color: #272c36;")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.Title = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Title.sizePolicy().hasHeightForWidth())
        self.Title.setSizePolicy(sizePolicy)
        self.Title.setStyleSheet("*{\n"
"padding:3 0;\n"
"background-color:#343b48;\n"
"}\n"
"\n"
"QFrame #rightTitle{\n"
"\n"
"border-bottom: 2px solid #3c4454;\n"
"\n"
"}\n"
"\n"
"\n"
"")
        self.Title.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.Title.setFrameShadow(QtWidgets.QFrame.Raised)
        self.Title.setObjectName("Title")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.Title)
        self.horizontalLayout_3.setContentsMargins(3, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.leftTitle = QtWidgets.QFrame(self.Title)
        self.leftTitle.setStyleSheet("")
        self.leftTitle.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.leftTitle.setFrameShadow(QtWidgets.QFrame.Raised)
        self.leftTitle.setObjectName("leftTitle")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.leftTitle)
        self.gridLayout_3.setContentsMargins(5, 0, 0, 0)
        self.gridLayout_3.setSpacing(0)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.btn_title = QtWidgets.QPushButton(self.leftTitle)
        self.btn_title.setStyleSheet("font:bold;")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(".\\images/images/setting.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_title.setIcon(icon)
        self.btn_title.setIconSize(QtCore.QSize(24, 24))
        self.btn_title.setObjectName("btn_title")
        self.gridLayout_3.addWidget(self.btn_title, 0, 0, 1, 1, QtCore.Qt.AlignLeft)
        self.horizontalLayout_3.addWidget(self.leftTitle)
        self.rightButtons = QtWidgets.QFrame(self.Title)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.rightButtons.sizePolicy().hasHeightForWidth())
        self.rightButtons.setSizePolicy(sizePolicy)
        self.rightButtons.setStyleSheet("\n"
"QPushButton:hover {\n"
"    background-color: #3c4454;\n"
"    border-radius:5px;\n"
"\n"
"\n"
"}\n"
"QPushButton:pressed { \n"
"    background-color: #2c313c;\n"
"     border-radius:5px;\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.rightButtons.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.rightButtons.setFrameShadow(QtWidgets.QFrame.Raised)
        self.rightButtons.setObjectName("rightButtons")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.rightButtons)
        self.horizontalLayout_2.setContentsMargins(0, 0, 6, 0)
        self.horizontalLayout_2.setSpacing(3)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.minimizeAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.minimizeAppBtn.setStyleSheet("")
        self.minimizeAppBtn.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(".\\images/icons/icon_minimize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.minimizeAppBtn.setIcon(icon1)
        self.minimizeAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.minimizeAppBtn.setObjectName("minimizeAppBtn")
        self.horizontalLayout_2.addWidget(self.minimizeAppBtn)
        self.maximizeRestoreAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.maximizeRestoreAppBtn.setStyleSheet("")
        self.maximizeRestoreAppBtn.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(".\\images/icons/icon_maximize.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.maximizeRestoreAppBtn.setIcon(icon2)
        self.maximizeRestoreAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.maximizeRestoreAppBtn.setObjectName("maximizeRestoreAppBtn")
        self.horizontalLayout_2.addWidget(self.maximizeRestoreAppBtn)
        self.closeAppBtn = QtWidgets.QPushButton(self.rightButtons)
        self.closeAppBtn.setStyleSheet("\n"
"QPushButton:pressed { \n"
"background-color: #ff5555;\n"
" border-style: solid; border-radius: 4px; \n"
"}\n"
"")
        self.closeAppBtn.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(".\\images/icons/icon_close.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.closeAppBtn.setIcon(icon3)
        self.closeAppBtn.setIconSize(QtCore.QSize(28, 24))
        self.closeAppBtn.setObjectName("closeAppBtn")
        self.horizontalLayout_2.addWidget(self.closeAppBtn)
        self.horizontalLayout_3.addWidget(self.rightButtons)
        self.verticalLayout.addWidget(self.Title)
        self.bodyPages = QtWidgets.QFrame(self.frame)
        self.bodyPages.setStyleSheet("")
        self.bodyPages.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bodyPages.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bodyPages.setObjectName("bodyPages")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.bodyPages)
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_4.setSpacing(0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.frameBody = QtWidgets.QFrame(self.bodyPages)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameBody.sizePolicy().hasHeightForWidth())
        self.frameBody.setSizePolicy(sizePolicy)
        self.frameBody.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.frameBody.setStyleSheet("*{\n"
" background-color: #2c313c;\n"
"}\n"
"QSpinBox {\n"
"     background-color: rgb(34, 38, 47);\n"
"}")
        self.frameBody.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameBody.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameBody.setObjectName("frameBody")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.frameBody)
        self.verticalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_10.setSpacing(0)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.frame_13 = QtWidgets.QFrame(self.frameBody)
        self.frame_13.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_13.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_13.setObjectName("frame_13")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.frame_13)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.groupBox = QtWidgets.QGroupBox(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox.sizePolicy().hasHeightForWidth())
        self.groupBox.setSizePolicy(sizePolicy)
        self.groupBox.setStyleSheet("")
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.label_22 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_22.sizePolicy().hasHeightForWidth())
        self.label_22.setSizePolicy(sizePolicy)
        self.label_22.setStyleSheet("color: #c3ccdf;")
        self.label_22.setObjectName("label_22")
        self.horizontalLayout_9.addWidget(self.label_22)
        self.selectedTasks = QtWidgets.QComboBox(self.groupBox)
        self.selectedTasks.setStyleSheet("margin:5 5 5 5;")
        self.selectedTasks.setObjectName("selectedTasks")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(".\\images/images/love.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.selectedTasks.addItem(icon4, "")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(".\\images/images/followers.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.selectedTasks.addItem(icon5, "")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(".\\images/images/comment.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.selectedTasks.addItem(icon6, "")
        self.horizontalLayout_9.addWidget(self.selectedTasks)
        self.verticalLayout_4.addLayout(self.horizontalLayout_9)
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.label_57 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_57.sizePolicy().hasHeightForWidth())
        self.label_57.setSizePolicy(sizePolicy)
        self.label_57.setStyleSheet("color: #c3ccdf;")
        self.label_57.setObjectName("label_57")
        self.horizontalLayout_11.addWidget(self.label_57)
        self.checkVirtual = QtWidgets.QComboBox(self.groupBox)
        self.checkVirtual.setStyleSheet("margin:5 5 5 5;")
        self.checkVirtual.setObjectName("checkVirtual")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(".\\images/images/close.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.checkVirtual.addItem(icon7, "")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap(".\\images/images/wait.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.checkVirtual.addItem(icon8, "")
        icon9 = QtGui.QIcon()
        icon9.addPixmap(QtGui.QPixmap(".\\images/images/loading-arrow.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.checkVirtual.addItem(icon9, "")
        self.horizontalLayout_11.addWidget(self.checkVirtual)
        self.verticalLayout_4.addLayout(self.horizontalLayout_11)
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.label_23 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_23.sizePolicy().hasHeightForWidth())
        self.label_23.setSizePolicy(sizePolicy)
        self.label_23.setObjectName("label_23")
        self.horizontalLayout_12.addWidget(self.label_23)
        self.delayGetJob = QtWidgets.QSpinBox(self.groupBox)
        self.delayGetJob.setObjectName("delayGetJob")
        self.horizontalLayout_12.addWidget(self.delayGetJob)
        self.label_24 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_24.sizePolicy().hasHeightForWidth())
        self.label_24.setSizePolicy(sizePolicy)
        self.label_24.setObjectName("label_24")
        self.horizontalLayout_12.addWidget(self.label_24)
        self.delayGetXu = QtWidgets.QSpinBox(self.groupBox)
        self.delayGetXu.setProperty("value", 8)
        self.delayGetXu.setObjectName("delayGetXu")
        self.horizontalLayout_12.addWidget(self.delayGetXu)
        self.label_25 = QtWidgets.QLabel(self.groupBox)
        self.label_25.setObjectName("label_25")
        self.horizontalLayout_12.addWidget(self.label_25)
        self.verticalLayout_4.addLayout(self.horizontalLayout_12)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_42 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_42.sizePolicy().hasHeightForWidth())
        self.label_42.setSizePolicy(sizePolicy)
        self.label_42.setObjectName("label_42")
        self.horizontalLayout_4.addWidget(self.label_42)
        self.delayWaitJob1 = QtWidgets.QSpinBox(self.groupBox)
        self.delayWaitJob1.setObjectName("delayWaitJob1")
        self.horizontalLayout_4.addWidget(self.delayWaitJob1)
        self.label_43 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_43.sizePolicy().hasHeightForWidth())
        self.label_43.setSizePolicy(sizePolicy)
        self.label_43.setObjectName("label_43")
        self.horizontalLayout_4.addWidget(self.label_43)
        self.delayWaitJob2 = QtWidgets.QSpinBox(self.groupBox)
        self.delayWaitJob2.setObjectName("delayWaitJob2")
        self.horizontalLayout_4.addWidget(self.delayWaitJob2)
        self.label = QtWidgets.QLabel(self.groupBox)
        self.label.setObjectName("label")
        self.horizontalLayout_4.addWidget(self.label)
        self.verticalLayout_4.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.label_26 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_26.sizePolicy().hasHeightForWidth())
        self.label_26.setSizePolicy(sizePolicy)
        self.label_26.setObjectName("label_26")
        self.horizontalLayout_13.addWidget(self.label_26)
        self.nextAccFaild = QtWidgets.QSpinBox(self.groupBox)
        self.nextAccFaild.setMaximum(999)
        self.nextAccFaild.setProperty("value", 10)
        self.nextAccFaild.setObjectName("nextAccFaild")
        self.horizontalLayout_13.addWidget(self.nextAccFaild)
        self.label_27 = QtWidgets.QLabel(self.groupBox)
        self.label_27.setObjectName("label_27")
        self.horizontalLayout_13.addWidget(self.label_27)
        self.rateNext = QtWidgets.QSpinBox(self.groupBox)
        self.rateNext.setProperty("value", 10)
        self.rateNext.setObjectName("rateNext")
        self.horizontalLayout_13.addWidget(self.rateNext)
        self.label_28 = QtWidgets.QLabel(self.groupBox)
        self.label_28.setObjectName("label_28")
        self.horizontalLayout_13.addWidget(self.label_28)
        self.verticalLayout_4.addLayout(self.horizontalLayout_13)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.label_29 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_29.sizePolicy().hasHeightForWidth())
        self.label_29.setSizePolicy(sizePolicy)
        self.label_29.setObjectName("label_29")
        self.horizontalLayout_14.addWidget(self.label_29)
        self.delayCH = QtWidgets.QSpinBox(self.groupBox)
        self.delayCH.setProperty("value", 30)
        self.delayCH.setObjectName("delayCH")
        self.horizontalLayout_14.addWidget(self.delayCH)
        self.label_30 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_30.sizePolicy().hasHeightForWidth())
        self.label_30.setSizePolicy(sizePolicy)
        self.label_30.setObjectName("label_30")
        self.horizontalLayout_14.addWidget(self.label_30)
        self.verticalLayout_4.addLayout(self.horizontalLayout_14)
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.upAvatar = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.upAvatar.sizePolicy().hasHeightForWidth())
        self.upAvatar.setSizePolicy(sizePolicy)
        self.upAvatar.setText("")
        self.upAvatar.setObjectName("upAvatar")
        self.horizontalLayout_15.addWidget(self.upAvatar)
        self.label_4 = QtWidgets.QLabel(self.groupBox)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_15.addWidget(self.label_4)
        self.upVideo = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.upVideo.sizePolicy().hasHeightForWidth())
        self.upVideo.setSizePolicy(sizePolicy)
        self.upVideo.setText("")
        self.upVideo.setObjectName("upVideo")
        self.horizontalLayout_15.addWidget(self.upVideo)
        self.label_6 = QtWidgets.QLabel(self.groupBox)
        self.label_6.setObjectName("label_6")
        self.horizontalLayout_15.addWidget(self.label_6)
        self.verticalLayout_4.addLayout(self.horizontalLayout_15)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.publicLove = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.publicLove.sizePolicy().hasHeightForWidth())
        self.publicLove.setSizePolicy(sizePolicy)
        self.publicLove.setText("")
        self.publicLove.setObjectName("publicLove")
        self.horizontalLayout_7.addWidget(self.publicLove)
        self.label_8 = QtWidgets.QLabel(self.groupBox)
        self.label_8.setObjectName("label_8")
        self.horizontalLayout_7.addWidget(self.label_8)
        self.reopenChrome = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.reopenChrome.sizePolicy().hasHeightForWidth())
        self.reopenChrome.setSizePolicy(sizePolicy)
        self.reopenChrome.setText("")
        self.reopenChrome.setObjectName("reopenChrome")
        self.horizontalLayout_7.addWidget(self.reopenChrome)
        self.label_12 = QtWidgets.QLabel(self.groupBox)
        self.label_12.setObjectName("label_12")
        self.horizontalLayout_7.addWidget(self.label_12)
        self.verticalLayout_4.addLayout(self.horizontalLayout_7)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.bypassCaptcha = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.bypassCaptcha.sizePolicy().hasHeightForWidth())
        self.bypassCaptcha.setSizePolicy(sizePolicy)
        self.bypassCaptcha.setText("")
        self.bypassCaptcha.setObjectName("bypassCaptcha")
        self.horizontalLayout_6.addWidget(self.bypassCaptcha)
        self.label_7 = QtWidgets.QLabel(self.groupBox)
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_6.addWidget(self.label_7)
        self.verticalLayout_4.addLayout(self.horizontalLayout_6)
        self.groupBox_17 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_17.setStyleSheet("")
        self.groupBox_17.setObjectName("groupBox_17")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_17)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.label_31 = QtWidgets.QLabel(self.groupBox_17)
        self.label_31.setObjectName("label_31")
        self.horizontalLayout_16.addWidget(self.label_31)
        self.maxJobFollow = QtWidgets.QSpinBox(self.groupBox_17)
        self.maxJobFollow.setMaximum(99999)
        self.maxJobFollow.setProperty("value", 9999)
        self.maxJobFollow.setObjectName("maxJobFollow")
        self.horizontalLayout_16.addWidget(self.maxJobFollow)
        self.label_49 = QtWidgets.QLabel(self.groupBox_17)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_49.sizePolicy().hasHeightForWidth())
        self.label_49.setSizePolicy(sizePolicy)
        self.label_49.setObjectName("label_49")
        self.horizontalLayout_16.addWidget(self.label_49)
        self.cacheFollow = QtWidgets.QSpinBox(self.groupBox_17)
        self.cacheFollow.setStyleSheet("")
        self.cacheFollow.setMaximum(30)
        self.cacheFollow.setProperty("value", 10)
        self.cacheFollow.setObjectName("cacheFollow")
        self.horizontalLayout_16.addWidget(self.cacheFollow)
        self.label_55 = QtWidgets.QLabel(self.groupBox_17)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_55.sizePolicy().hasHeightForWidth())
        self.label_55.setSizePolicy(sizePolicy)
        self.label_55.setObjectName("label_55")
        self.horizontalLayout_16.addWidget(self.label_55)
        self.verticalLayout_2.addLayout(self.horizontalLayout_16)
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.label_58 = QtWidgets.QLabel(self.groupBox_17)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_58.sizePolicy().hasHeightForWidth())
        self.label_58.setSizePolicy(sizePolicy)
        self.label_58.setObjectName("label_58")
        self.horizontalLayout_17.addWidget(self.label_58)
        self.methodFollow = QtWidgets.QComboBox(self.groupBox_17)
        self.methodFollow.setStyleSheet("")
        self.methodFollow.setObjectName("methodFollow")
        self.methodFollow.addItem("")
        self.methodFollow.addItem("")
        self.horizontalLayout_17.addWidget(self.methodFollow)
        self.verticalLayout_2.addLayout(self.horizontalLayout_17)
        self.verticalLayout_4.addWidget(self.groupBox_17)
        self.groupBox_18 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_18.setStyleSheet("")
        self.groupBox_18.setObjectName("groupBox_18")
        self.verticalLayout_27 = QtWidgets.QVBoxLayout(self.groupBox_18)
        self.verticalLayout_27.setObjectName("verticalLayout_27")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.label_32 = QtWidgets.QLabel(self.groupBox_18)
        self.label_32.setObjectName("label_32")
        self.horizontalLayout_18.addWidget(self.label_32)
        self.maxJobLove = QtWidgets.QSpinBox(self.groupBox_18)
        self.maxJobLove.setMaximum(99999)
        self.maxJobLove.setProperty("value", 9999)
        self.maxJobLove.setObjectName("maxJobLove")
        self.horizontalLayout_18.addWidget(self.maxJobLove)
        self.label_50 = QtWidgets.QLabel(self.groupBox_18)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_50.sizePolicy().hasHeightForWidth())
        self.label_50.setSizePolicy(sizePolicy)
        self.label_50.setObjectName("label_50")
        self.horizontalLayout_18.addWidget(self.label_50)
        self.cacheLove = QtWidgets.QSpinBox(self.groupBox_18)
        self.cacheLove.setStyleSheet("")
        self.cacheLove.setMaximum(30)
        self.cacheLove.setProperty("value", 10)
        self.cacheLove.setObjectName("cacheLove")
        self.horizontalLayout_18.addWidget(self.cacheLove)
        self.label_59 = QtWidgets.QLabel(self.groupBox_18)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_59.sizePolicy().hasHeightForWidth())
        self.label_59.setSizePolicy(sizePolicy)
        self.label_59.setObjectName("label_59")
        self.horizontalLayout_18.addWidget(self.label_59)
        self.verticalLayout_27.addLayout(self.horizontalLayout_18)
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.label_60 = QtWidgets.QLabel(self.groupBox_18)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_60.sizePolicy().hasHeightForWidth())
        self.label_60.setSizePolicy(sizePolicy)
        self.label_60.setObjectName("label_60")
        self.horizontalLayout_19.addWidget(self.label_60)
        self.methodLove = QtWidgets.QComboBox(self.groupBox_18)
        self.methodLove.setStyleSheet("")
        self.methodLove.setObjectName("methodLove")
        self.methodLove.addItem("")
        self.methodLove.addItem("")
        self.horizontalLayout_19.addWidget(self.methodLove)
        self.verticalLayout_27.addLayout(self.horizontalLayout_19)
        self.verticalLayout_4.addWidget(self.groupBox_18)
        self.groupBox_4 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_4.setObjectName("groupBox_4")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.groupBox_4)
        self.horizontalLayout_5.setContentsMargins(9, 12, -1, -1)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.tokenTLC = QtWidgets.QLineEdit(self.groupBox_4)
        self.tokenTLC.setStyleSheet("QLineEdit {\n"
"\n"
"    border-radius: 5px;\n"
"    padding:3px;\n"
"\n"
"    padding-left: 10px;\n"
"    selection-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(255, 121, 198);\n"
"}")
        self.tokenTLC.setClearButtonEnabled(True)
        self.tokenTLC.setObjectName("tokenTLC")
        self.horizontalLayout_5.addWidget(self.tokenTLC)
        self.verticalLayout_4.addWidget(self.groupBox_4)
        self.horizontalLayout_20.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_2.sizePolicy().hasHeightForWidth())
        self.groupBox_2.setSizePolicy(sizePolicy)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.frame_24 = QtWidgets.QFrame(self.groupBox_2)
        self.frame_24.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_24.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_24.setObjectName("frame_24")
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout(self.frame_24)
        self.horizontalLayout_23.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.label_33 = QtWidgets.QLabel(self.frame_24)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_33.sizePolicy().hasHeightForWidth())
        self.label_33.setSizePolicy(sizePolicy)
        self.label_33.setStyleSheet("color: #c3ccdf;")
        self.label_33.setObjectName("label_33")
        self.horizontalLayout_23.addWidget(self.label_33)
        self.Browser = QtWidgets.QComboBox(self.frame_24)
        self.Browser.setObjectName("Browser")
        icon10 = QtGui.QIcon()
        icon10.addPixmap(QtGui.QPixmap(".\\images/images/chrome.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Browser.addItem(icon10, "")
        icon11 = QtGui.QIcon()
        icon11.addPixmap(QtGui.QPixmap(".\\images/images/lion.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Browser.addItem(icon11, "")
        icon12 = QtGui.QIcon()
        icon12.addPixmap(QtGui.QPixmap(".\\images/images/45863095.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Browser.addItem(icon12, "")
        icon13 = QtGui.QIcon()
        icon13.addPixmap(QtGui.QPixmap(".\\images/images/gologin.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Browser.addItem(icon13, "")
        icon15 = QtGui.QIcon()
        icon15.addPixmap(QtGui.QPixmap(".\\images/images/hidemyacc.jpg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Browser.addItem(icon15, "")
        icon14 = QtGui.QIcon()
        icon14.addPixmap(QtGui.QPixmap(".\\images/images/other.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.Browser.addItem(icon14, "")
        self.horizontalLayout_23.addWidget(self.Browser)
        self.verticalLayout_3.addWidget(self.frame_24)
        self.frame_25 = QtWidgets.QFrame(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_25.sizePolicy().hasHeightForWidth())
        self.frame_25.setSizePolicy(sizePolicy)
        self.frame_25.setStyleSheet("*{\n"
"    background-color:#343b48;\n"
"}\n"
"QLineEdit{\n"
"background-color:rgba(0, 0, 0, 0);\n"
"border:1px solid rgba(0, 0, 0, 0);\n"
"border-bottom-color:rgba(46, 82, 101, 255);\n"
"color:rgb(255, 255, 255);\n"
"padding-bottom:7px;\n"
"}\n"
"\n"
"")
        self.frame_25.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_25.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_25.setObjectName("frame_25")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.frame_25)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.label_34 = QtWidgets.QLabel(self.frame_25)
        self.label_34.setObjectName("label_34")
        self.verticalLayout_8.addWidget(self.label_34)
        self.pathChrome = QtWidgets.QLineEdit(self.frame_25)
        self.pathChrome.setStyleSheet("")
        self.pathChrome.setClearButtonEnabled(True)
        self.pathChrome.setObjectName("pathChrome")
        self.verticalLayout_8.addWidget(self.pathChrome)
        self.versionChrome = QtWidgets.QLineEdit(self.frame_25)
        self.versionChrome.setClearButtonEnabled(True)
        self.versionChrome.setObjectName("versionChrome")
        self.verticalLayout_8.addWidget(self.versionChrome)
        self.verticalLayout_3.addWidget(self.frame_25)
        self.frame_26 = QtWidgets.QFrame(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_26.sizePolicy().hasHeightForWidth())
        self.frame_26.setSizePolicy(sizePolicy)
        self.frame_26.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_26.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_26.setObjectName("frame_26")
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout(self.frame_26)
        self.horizontalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_24.setSpacing(0)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.label_35 = QtWidgets.QLabel(self.frame_26)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_35.sizePolicy().hasHeightForWidth())
        self.label_35.setSizePolicy(sizePolicy)
        self.label_35.setStyleSheet("color: #c3ccdf;")
        self.label_35.setObjectName("label_35")
        self.horizontalLayout_24.addWidget(self.label_35)
        self.webCoin = QtWidgets.QComboBox(self.frame_26)
        self.webCoin.setStyleSheet("margin:5 5 5 5;")
        self.webCoin.setObjectName("webCoin")
        self.webCoin.addItem("")
        self.webCoin.addItem("")
        self.webCoin.addItem("")
        self.horizontalLayout_24.addWidget(self.webCoin)
        self.verticalLayout_3.addWidget(self.frame_26)
        self.frame_27 = QtWidgets.QFrame(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_27.sizePolicy().hasHeightForWidth())
        self.frame_27.setSizePolicy(sizePolicy)
        self.frame_27.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_27.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_27.setObjectName("frame_27")
        self.horizontalLayout_29 = QtWidgets.QHBoxLayout(self.frame_27)
        self.horizontalLayout_29.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_29.setSpacing(3)
        self.horizontalLayout_29.setObjectName("horizontalLayout_29")
        self.label_36 = QtWidgets.QLabel(self.frame_27)
        self.label_36.setObjectName("label_36")
        self.horizontalLayout_29.addWidget(self.label_36)
        self.maxThreadChrome = QtWidgets.QSpinBox(self.frame_27)
        self.maxThreadChrome.setProperty("value", 10)
        self.maxThreadChrome.setObjectName("maxThreadChrome")
        self.maxThreadChrome.setMaximum(999)
        self.horizontalLayout_29.addWidget(self.maxThreadChrome)
        self.label_37 = QtWidgets.QLabel(self.frame_27)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_37.sizePolicy().hasHeightForWidth())
        self.label_37.setSizePolicy(sizePolicy)
        self.label_37.setObjectName("label_37")
        self.horizontalLayout_29.addWidget(self.label_37)
        self.delayOpenChrome = QtWidgets.QSpinBox(self.frame_27)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.delayOpenChrome.sizePolicy().hasHeightForWidth())
        self.delayOpenChrome.setSizePolicy(sizePolicy)
        self.delayOpenChrome.setMaximum(9999)
        self.delayOpenChrome.setProperty("value", 15)
        self.delayOpenChrome.setObjectName("delayOpenChrome")
        self.horizontalLayout_29.addWidget(self.delayOpenChrome)
        self.verticalLayout_3.addWidget(self.frame_27)
        self.frame_28 = QtWidgets.QFrame(self.groupBox_2)
        self.frame_28.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_28.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_28.setObjectName("frame_28")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.frame_28)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.label_38 = QtWidgets.QLabel(self.frame_28)
        self.label_38.setObjectName("label_38")
        self.horizontalLayout_30.addWidget(self.label_38)
        self.widthChrome = QtWidgets.QSpinBox(self.frame_28)
        self.widthChrome.setMaximum(9000000)
        self.widthChrome.setProperty("value", 1200)
        self.widthChrome.setObjectName("widthChrome")
        self.horizontalLayout_30.addWidget(self.widthChrome)
        self.label_39 = QtWidgets.QLabel(self.frame_28)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_39.sizePolicy().hasHeightForWidth())
        self.label_39.setSizePolicy(sizePolicy)
        self.label_39.setStyleSheet("font:bold;")
        self.label_39.setObjectName("label_39")
        self.horizontalLayout_30.addWidget(self.label_39)
        self.heightChrome = QtWidgets.QSpinBox(self.frame_28)
        self.heightChrome.setMaximum(9000000)
        self.heightChrome.setProperty("value", 1450)
        self.heightChrome.setObjectName("heightChrome")
        self.horizontalLayout_30.addWidget(self.heightChrome)
        self.minisizeChrome = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.minisizeChrome.sizePolicy().hasHeightForWidth())
        self.minisizeChrome.setSizePolicy(sizePolicy)
        self.minisizeChrome.setText("")
        self.minisizeChrome.setObjectName("minisizeChrome")
        self.horizontalLayout_30.addWidget(self.minisizeChrome)
        self.label_10 = QtWidgets.QLabel(self.frame_28)
        self.label_10.setObjectName("label_10")
        self.horizontalLayout_30.addWidget(self.label_10)
        self.verticalLayout_3.addWidget(self.frame_28)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.proxyRequests = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.proxyRequests.sizePolicy().hasHeightForWidth())
        self.proxyRequests.setSizePolicy(sizePolicy)
        self.proxyRequests.setText("")
        self.proxyRequests.setObjectName("proxyRequests")
        self.horizontalLayout_8.addWidget(self.proxyRequests)
        self.label_11 = QtWidgets.QLabel(self.groupBox_2)
        self.label_11.setObjectName("label_11")
        self.horizontalLayout_8.addWidget(self.label_11)
        self.verticalLayout_3.addLayout(self.horizontalLayout_8)
        self.proxyImport = QtWidgets.QPlainTextEdit(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.proxyImport.sizePolicy().hasHeightForWidth())
        self.proxyImport.setSizePolicy(sizePolicy)
        self.proxyImport.setStyleSheet("\n"
"background-color:#343b48;")
        self.proxyImport.setPlainText("")
        self.proxyImport.setObjectName("proxyImport")
        self.verticalLayout_3.addWidget(self.proxyImport)
        self.btnSave = QtWidgets.QPushButton(self.groupBox_2)
        self.btnSave.setStyleSheet("* {\n"
"    background-color: #1b1e23;\n"
"    border-radius:3px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    height: 40px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #272c36;\n"
"\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #1b1e23;\n"
"\n"
"}")
        self.btnSave.setObjectName("btnSave")
        self.verticalLayout_3.addWidget(self.btnSave)
        self.horizontalLayout_20.addWidget(self.groupBox_2)
        self.groupBox_3 = QtWidgets.QGroupBox(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_3.sizePolicy().hasHeightForWidth())
        self.groupBox_3.setSizePolicy(sizePolicy)
        self.groupBox_3.setObjectName("groupBox_3")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.groupBox_3)
        self.gridLayout_2.setContentsMargins(3, 9, 3, 3)
        self.gridLayout_2.setHorizontalSpacing(3)
        self.gridLayout_2.setVerticalSpacing(0)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.scrollArea_2 = QtWidgets.QScrollArea(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scrollArea_2.sizePolicy().hasHeightForWidth())
        self.scrollArea_2.setSizePolicy(sizePolicy)
        self.scrollArea_2.setWidgetResizable(True)
        self.scrollArea_2.setObjectName("scrollArea_2")
        self.scrollExtension = QtWidgets.QWidget()
        self.scrollExtension.setGeometry(QtCore.QRect(0, 0, 353, 523))
        self.scrollExtension.setObjectName("scrollExtension")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.scrollExtension)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.frameExten1 = QtWidgets.QFrame(self.scrollExtension)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameExten1.sizePolicy().hasHeightForWidth())
        self.frameExten1.setSizePolicy(sizePolicy)
        self.frameExten1.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frameExten1.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frameExten1.setObjectName("frameExten1")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.frameExten1)
        self.horizontalLayout_31.setContentsMargins(0, 5, 6, 0)
        self.horizontalLayout_31.setSpacing(6)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        self.btnEx1 = QtWidgets.QPushButton(self.frameExten1)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnEx1.sizePolicy().hasHeightForWidth())
        self.btnEx1.setSizePolicy(sizePolicy)
        self.btnEx1.setStyleSheet("")
        icon15 = QtGui.QIcon()
        icon15.addPixmap(QtGui.QPixmap(".\\images/images/128.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btnEx1.setIcon(icon15)
        self.btnEx1.setIconSize(QtCore.QSize(24, 24))
        self.btnEx1.setObjectName("btnEx1")
        self.horizontalLayout_31.addWidget(self.btnEx1)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_31.addItem(spacerItem)
        self.cboxEX1 = PyToggle()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.cboxEX1.sizePolicy().hasHeightForWidth())
        self.cboxEX1.setSizePolicy(sizePolicy)
        self.cboxEX1.setStyleSheet("")
        self.cboxEX1.setText("")
        self.cboxEX1.setObjectName("cboxEX1")
        self.horizontalLayout_31.addWidget(self.cboxEX1)
        self.btnCloseEx1 = QtWidgets.QPushButton(self.frameExten1)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCloseEx1.sizePolicy().hasHeightForWidth())
        self.btnCloseEx1.setSizePolicy(sizePolicy)
        self.btnCloseEx1.setStyleSheet("QPushButton{\n"
"border-radius:15px;\n"
"text-align:center;\n"
"width:10;\n"
"height:8;\n"
"padding-right:11;\n"
"padding-left:10;\n"
"padding-top:10;\n"
"padding-bottom:10;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#343b48;\n"
"    border-radius:2px;\n"
"\n"
"}\n"
"\n"
"\n"
"QPushButton:pressed { \n"
"background-color: #ff5555;\n"
" border-style: solid; border-radius: 2px; \n"
"}\n"
"")
        self.btnCloseEx1.setText("")
        icon16 = QtGui.QIcon()
        icon16.addPixmap(QtGui.QPixmap(".\\images/images/delete_sign_25px.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btnCloseEx1.setIcon(icon16)
        self.btnCloseEx1.setIconSize(QtCore.QSize(24, 24))
        self.btnCloseEx1.setObjectName("btnCloseEx1")
        self.horizontalLayout_31.addWidget(self.btnCloseEx1)
        self.verticalLayout_9.addWidget(self.frameExten1)
        spacerItem1 = QtWidgets.QSpacerItem(20, 541, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_9.addItem(spacerItem1)
        self.scrollArea_2.setWidget(self.scrollExtension)
        self.gridLayout_2.addWidget(self.scrollArea_2, 0, 0, 1, 1)
        self.btnUploadEX = QtWidgets.QPushButton(self.groupBox_3)
        self.btnUploadEX.setStyleSheet("* {\n"
"    background-color: #1b1e23;\n"
"    border-radius:3px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    height: 40px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #272c36;\n"
"\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #1b1e23;\n"
"\n"
"}")
        icon17 = QtGui.QIcon()
        icon17.addPixmap(QtGui.QPixmap(".\\images/images/up-loading.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btnUploadEX.setIcon(icon17)
        self.btnUploadEX.setIconSize(QtCore.QSize(24, 24))
        self.btnUploadEX.setObjectName("btnUploadEX")
        self.gridLayout_2.addWidget(self.btnUploadEX, 1, 0, 1, 1)
        self.horizontalLayout_20.addWidget(self.groupBox_3)
        self.verticalLayout_10.addWidget(self.frame_13)
        self.gridLayout_4.addWidget(self.frameBody, 0, 0, 1, 1)
        self.verticalLayout.addWidget(self.bodyPages)
        self.bottom = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.bottom.sizePolicy().hasHeightForWidth())
        self.bottom.setSizePolicy(sizePolicy)
        self.bottom.setStyleSheet("*{\n"
"    background-color: #181818;\n"
"    color:#5f8899;\n"
"    height:30px;\n"
"}\n"
"\n"
"")
        self.bottom.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bottom.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bottom.setObjectName("bottom")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.bottom)
        self.horizontalLayout_10.setContentsMargins(5, 5, 5, 5)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.lblCoppyright = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblCoppyright.sizePolicy().hasHeightForWidth())
        self.lblCoppyright.setSizePolicy(sizePolicy)
        self.lblCoppyright.setObjectName("lblCoppyright")
        self.horizontalLayout_10.addWidget(self.lblCoppyright)
        self.lblPyTournes1 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes1.sizePolicy().hasHeightForWidth())
        self.lblPyTournes1.setSizePolicy(sizePolicy)
        self.lblPyTournes1.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes1.setObjectName("lblPyTournes1")
        self.horizontalLayout_10.addWidget(self.lblPyTournes1)
        self.label_3 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setStyleSheet("")
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_10.addWidget(self.label_3)
        self.lblPyTournes2 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes2.sizePolicy().hasHeightForWidth())
        self.lblPyTournes2.setSizePolicy(sizePolicy)
        self.lblPyTournes2.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes2.setObjectName("lblPyTournes2")
        self.horizontalLayout_10.addWidget(self.lblPyTournes2)
        self.label_5 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_10.addWidget(self.label_5)
        self.lblPyTournes3 = QtWidgets.QLabel(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lblPyTournes3.sizePolicy().hasHeightForWidth())
        self.lblPyTournes3.setSizePolicy(sizePolicy)
        self.lblPyTournes3.setStyleSheet("QLabel {\n"
"color:#5fbc5a;\n"
"font:bold;\n"
"}\n"
"QLabel:hover{\n"
"    color:#ff0000;\n"
"}\n"
"")
        self.lblPyTournes3.setObjectName("lblPyTournes3")
        self.horizontalLayout_10.addWidget(self.lblPyTournes3)
        self.label_9 = QtWidgets.QLabel(self.bottom)
        self.label_9.setObjectName("label_9")
        self.horizontalLayout_10.addWidget(self.label_9)
        self.label_2 = QtWidgets.QLabel(self.bottom)
        self.label_2.setText("")
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_10.addWidget(self.label_2)
        self.pushButton = QtWidgets.QPushButton(self.bottom)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton.sizePolicy().hasHeightForWidth())
        self.pushButton.setSizePolicy(sizePolicy)
        self.pushButton.setText("")
        self.pushButton.setObjectName("pushButton")
        self.horizontalLayout_10.addWidget(self.pushButton)
        self.verticalLayout.addWidget(self.bottom)
        self.horizontalLayout.addWidget(self.frame)
        self.gridLayout_6.addWidget(self.MainPages, 0, 0, 1, 1)
        Settings.setCentralWidget(self.stylesheet)

        self.retranslateUi(Settings)
        QtCore.QMetaObject.connectSlotsByName(Settings)

    def retranslateUi(self, Settings):
        _translate = QtCore.QCoreApplication.translate
        Settings.setWindowTitle(_translate("Settings", "PyTournes - SETTINGS"))
        self.btn_title.setText(_translate("Settings", "Settings Page"))
        self.groupBox.setTitle(_translate("Settings", "GENERAL SETTINGS "))
        self.label_22.setText(_translate("Settings", "TASKS EXPLOITATION:"))
        self.selectedTasks.setItemText(0, _translate("Settings", "LOVE"))
        self.selectedTasks.setItemText(1, _translate("Settings", "FOLLOW"))
        self.selectedTasks.setItemText(2, _translate("Settings", "COMMENT"))
        self.label_57.setText(_translate("Settings", "CHECK VIRTUAL TASK:"))
        self.checkVirtual.setItemText(0, _translate("Settings", "Không kiểm tra"))
        self.checkVirtual.setItemText(1, _translate("Settings", "Đợi 6 giây"))
        self.checkVirtual.setItemText(2, _translate("Settings", "Reload trang"))
        self.label_23.setText(_translate("Settings", "Làm nhiệm vụ tiếp sau"))
        self.label_24.setText(_translate("Settings", "giây, Nhận xu sau:  "))
        self.label_25.setText(_translate("Settings", "giây"))
        self.label_42.setText(_translate("Settings", "Lấy nhiệm vụ thất bại đợi:"))
        self.label_43.setText(_translate("Settings", "đến"))
        self.label.setText(_translate("Settings", "giây"))
        self.label_26.setText(_translate("Settings", "Chuyển tài khoản khi nhận:"))
        self.label_27.setText(_translate("Settings", "lần xu <="))
        self.label_28.setText(_translate("Settings", "%"))
        self.label_29.setText(_translate("Settings", "Chờ lấy nhiệm vu sau khi cấu hình:"))
        self.label_30.setText(_translate("Settings", "giây"))
        self.label_4.setText(_translate("Settings", "Upload Avatar"))
        self.label_6.setText(_translate("Settings", "Upload Video"))
        self.label_8.setText(_translate("Settings", "Public Love"))
        self.label_12.setText(_translate("Settings", "ReOpen Chrome"))
        self.label_7.setText(_translate("Settings", "Do not check captcha"))
        self.groupBox_17.setTitle(_translate("Settings", "Task Follow"))
        self.label_31.setText(_translate("Settings", "Tối đa:"))
        self.label_49.setText(_translate("Settings", "JOB"))
        self.label_55.setText(_translate("Settings", "CAHE"))
        self.label_58.setText(_translate("Settings", "Cách làm:    "))
        self.methodFollow.setItemText(0, _translate("Settings", "DEFAULT"))
        self.methodFollow.setItemText(1, _translate("Settings", "FOLLOW SEARCH"))
        self.groupBox_18.setTitle(_translate("Settings", "Task Love"))
        self.label_32.setText(_translate("Settings", "Tối đa:"))
        self.label_50.setText(_translate("Settings", "JOB"))
        self.label_59.setText(_translate("Settings", "CAHE"))
        self.label_60.setText(_translate("Settings", "Cách làm:    "))
        self.methodLove.setItemText(0, _translate("Settings", "DEFAULT"))
        self.methodLove.setItemText(1, _translate("Settings", "DOUBLE CLICK LOVE"))
        self.groupBox_4.setTitle(_translate("Settings", "TOKEN TLC"))
        self.tokenTLC.setPlaceholderText(_translate("Settings", "API TOKEN TLC"))
        self.groupBox_2.setTitle(_translate("Settings", "SETTINGS BROWSER"))
        self.label_33.setText(_translate("Settings", "BROWSER:"))
        self.Browser.setItemText(0, _translate("Settings", "Chrome"))
        self.Browser.setItemText(1, _translate("Settings", "Brave"))
        self.Browser.setItemText(2, _translate("Settings", "Thorium"))
        self.Browser.setItemText(3, _translate("Settings", "GoLogin"))
        self.Browser.setItemText(4, _translate("Settings", "HideMyAcc"))
        self.Browser.setItemText(5, _translate("Settings", "Other"))
        self.label_34.setText(_translate("Settings", "<html><head/><body><p align=\"center\"><span style=\" color:#ff5500;\">Sử dụng trình duyệt tùy chọn vui lòng điền đường dẫn tới exe browser</span></p><p align=\"center\"><span style=\" color:#ff5500;\">Và điền version của browser xuống dưới</span></p></body></html>"))
        self.pathChrome.setPlaceholderText(_translate("Settings", "PATH BROWSER OR API BROWSER"))
        self.versionChrome.setPlaceholderText(_translate("Settings", "NULL"))
        self.label_35.setText(_translate("Settings", "WEBSITE:"))
        self.webCoin.setItemText(0, _translate("Settings", "TRAODOISUB"))
        self.webCoin.setItemText(1, _translate("Settings", "TANGLIKECHEO"))
        self.webCoin.setItemText(2, _translate("Settings", "TUONGTACCHEO"))
        self.label_36.setText(_translate("Settings", "Chạy tối đa:"))
        self.label_37.setText(_translate("Settings", "luồng ,Open Browser ( s ):"))
        self.label_38.setText(_translate("Settings", "Kích thước Browser:"))
        self.label_39.setText(_translate("Settings", "X"))
        self.label_10.setText(_translate("Settings", "Thu nhỏ Chrome"))
        self.label_11.setText(_translate("Settings", "Sử dụng proxy để requests web xu"))
        self.proxyImport.setPlaceholderText(_translate("Settings", "Proxy khi lấy nhiệm vụ TDS/TTC mỗi dòng 1 Proxy"))
        self.btnSave.setText(_translate("Settings", "Lưu cài đặt chung"))
        self.groupBox_3.setTitle(_translate("Settings", "MANAGE EXTENSIONS"))
        self.btnEx1.setText(_translate("Settings", "WebGL"))
        self.btnUploadEX.setText(_translate("Settings", "Upload Extension"))
        self.lblCoppyright.setText(_translate("Settings", "Powered by "))
        self.lblPyTournes1.setText(_translate("Settings", "PyTournes Shell v1.5.1 "))
        self.label_3.setText(_translate("Settings", "- Developed by "))
        self.lblPyTournes2.setText(_translate("Settings", "PyTournes"))
        self.label_5.setText(_translate("Settings", ". Coppyright © 2023 "))
        self.lblPyTournes3.setText(_translate("Settings", "PyTournes"))
        self.label_9.setText(_translate("Settings", ". All rights reserved"))
        
        self.lblPyTournes1.setText(F"{SHELLVERSION}")

        self.APP = Settings
        
        # Tạo ra 4 grip để resize
        for i in range(4):grip = QSizeGrip(self.stylesheet);grip.resize(self.gripSize, self.gripSize);self.grips.append(grip)
        self.APP.moveEvent = self.APP
        self.APP.mousePressEvent = self.mousePressEvent
        self.APP.mouseMoveEvent = self.mouseMoveEvent

        self.maximizeRestoreAppBtn.setEnabled(False)
        self.minimizeAppBtn.setEnabled(False)
        self.closeAppBtn.clicked.connect(self.closeAPP)
        self.uiFuncions = UiFuncions(self)


        self.pathExt = f'{os.getcwd()}\\Extension'
        self.loadExtension()
        self.btnUploadEX.clicked.connect(self.uploadExt)

        self.btnSave.clicked.connect(self.saveSettings)
        self.Browser.currentIndexChanged.connect(self.pathChange)
        self.loadSettings()

        # Chức năng của titleBottom
        labels = [self.lblPyTournes1, self.lblPyTournes2, self.lblPyTournes3]
        for label in labels:label.mousePressEvent = self.openURL
        # - - - - - - - - - - - - - - - - - - - - - - - - - - -
    def openURL(self, event):
        QDesktopServices.openUrl(QUrl("https://zalo.me/0865894536"))

    """Chức năng của Settings"""
    def pathChange(self):
        if self.Browser.currentText() != 'Other' and self.Browser.currentText() != 'GoLogin' and self.Browser.currentText() != 'HideMyAcc': 
            if self.Browser.currentText() == 'Chrome':
                pathBrowser = os.path.abspath('C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe')
            if self.Browser.currentText() == 'Brave':
                pathBrowser = os.path.abspath('C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe')
            if self.Browser.currentText() == 'Thorium':
                pathBrowser = os.path.abspath('C:\\Users\\<USER>\\AppData\Local\\Thorium\\Application\\thorium.exe')
            if os.path.exists(pathBrowser):self.pathChrome.setText(pathBrowser)
            else:self.uiFuncions.MessageBoxShow(text=f"{self.Browser.currentText()} Không tìm thấy trình duyệt này!!!")

    def saveSettings(self):
        try:
            try:
                with open(f"{pathConfig}\\settings.json", "r") as file:
                    jsonData = json.load(file)
                    if jsonData == '': jsonData = {}
            except FileNotFoundError:
                jsonData = {}
            jsonData.update({
                'miningWeb'         : self.webCoin.currentText(),
                'checkVirtual'      : self.checkVirtual.currentText(),
                'maxJobLove'        : self.maxJobLove.value(),
                'cacheLove'         : self.cacheLove.value(),
                'methodLove'        : self.methodLove.currentText(),
                'maxJobFollow'      : self.maxJobFollow.value(),
                'cacheFollow'       : self.cacheFollow.value(),
                'methodFollow'      : self.methodFollow.currentText(),
                'Task'              : self.selectedTasks.currentText(),
                'nextAccFaild'      : self.nextAccFaild.value(),
                'rateNext'          : self.rateNext.value(),
                'bypassCaptcha'     : self.bypassCaptcha.isChecked(),
                'delayGetXuValue'   : self.delayGetXu.value(),
                'delayGetJobValue'  : self.delayGetJob.value(),
                'delayWaitJob1'     : self.delayWaitJob1.value(),
                'delayWaitJob2'     : self.delayWaitJob2.value(),
                'delayCH'           : self.delayCH.value(),
                'upAvatar'          : self.upAvatar.isChecked(),
                'upVideo'           : self.upVideo.isChecked(),
                'publicLove'        : self.publicLove.isChecked(),
                'tokenTLC'          : self.tokenTLC.text(),
                'Browser'           : self.Browser.currentText(),
                'pathChrome'        : self.pathChrome.text(),
                'versionChrome'     : self.versionChrome.text(),
                'maxThreadChrome'   : self.maxThreadChrome.value(),
                'delayOpenChrome'   : self.delayOpenChrome.value(),
                'widthChrome'       : self.widthChrome.value(),
                'heightChrome'      : self.heightChrome.value(),
                'minisizeChrome'    : self.minisizeChrome.isChecked(),
                'proxyRequests'     : self.proxyRequests.isChecked(),
                'proxyImport'       : self.proxyImport.toPlainText().strip(),
                'reopenChrome'      : self.reopenChrome.isChecked()
            })
            with open(f"{pathConfig}\\settings.json", "w+", encoding='utf-8') as outfile:
                json.dump(jsonData, outfile, indent=4)
            self.uiFuncions.MessageBoxShow(text="Lưu cài đặt chung thành công.")
            self.checkSettings()
        except:self.uiFuncions.MessageBoxShow(text="Lưu cài đặt chung thất bại!!!")

    def loadSettings(self):
        try:
            with open(f"{pathConfig}\\settings.json", "r") as file:
                jsonData = json.load(file)
            self.webCoin.setCurrentText(jsonData['miningWeb'])
            self.checkVirtual.setCurrentText(jsonData['checkVirtual'])
            self.maxJobLove.setValue(jsonData['maxJobLove'])
            self.cacheLove.setValue(jsonData['cacheLove'])
            self.methodLove.setCurrentText(jsonData['methodLove'])
            self.maxJobFollow.setValue(jsonData['maxJobFollow'])
            self.cacheFollow.setValue(jsonData['cacheFollow'])
            self.methodFollow.setCurrentText(jsonData['methodFollow'])
            self.selectedTasks.setCurrentText(jsonData['Task'])
            self.nextAccFaild.setValue(jsonData['nextAccFaild'])
            self.rateNext.setValue(jsonData['rateNext'])
            self.bypassCaptcha.setChecked(jsonData['bypassCaptcha'])
            self.delayGetXu.setValue(jsonData['delayGetXuValue'])
            self.delayGetJob.setValue(jsonData['delayGetJobValue'])
            self.delayWaitJob1.setValue(jsonData['delayWaitJob1'])
            self.delayWaitJob2.setValue(jsonData['delayWaitJob2'])
            self.delayCH.setValue(jsonData['delayCH'])
            self.upAvatar.setChecked(jsonData['upAvatar'])
            self.upVideo.setChecked(jsonData['upVideo'])
            self.publicLove.setChecked(jsonData['publicLove'])
            self.tokenTLC.setText(jsonData['tokenTLC'])
            self.Browser.setCurrentText(jsonData['Browser'])
            self.pathChrome.setText(jsonData['pathChrome'])
            self.versionChrome.setText(jsonData['versionChrome'])
            self.maxThreadChrome.setValue(jsonData['maxThreadChrome'])
            self.delayOpenChrome.setValue(jsonData['delayOpenChrome'])
            self.widthChrome.setValue(jsonData['widthChrome'])
            self.heightChrome.setValue(jsonData['heightChrome'])
            self.minisizeChrome.setChecked(jsonData['minisizeChrome'])
            self.proxyRequests.setChecked(jsonData['proxyRequests'])
            self.proxyImport.setPlainText(jsonData['proxyImport'])
            self.reopenChrome.setChecked(jsonData['reopenChrome'])
        except:pass

    def checkSettings(self):
        if self.proxyRequests.isChecked():
            if self.proxyImport.toPlainText() == '':
                self.uiFuncions.MessageBoxShow(text='Chưa nhập proxy để requests');return
            if len(self.proxyImport.toPlainText().strip().split('\n')) < self.maxThreadChrome.value()  :
                self.uiFuncions.MessageBoxShow(text=f'Vui lòng nhập đủ số proxy cho {self.maxThreadChrome.value()} luồng');return
        if self.Browser.currentText() != 'Other' and self.pathChrome.text() == '':
            self.uiFuncions.MessageBoxShow(text=f'Bạn chưa nhập Path Chrome!!!');return
        if self.webCoin.currentText() == 'TANGLIKECHEO':
            self.uiFuncions.MessageBoxShow(text=f'Chức auto TLC năng tạm thời chưa hoạt động!!!');return
        if self.webCoin.currentText() == 'TANGLIKECHEO' and len(self.tokenTLC.text()) <= 20:
            self.uiFuncions.MessageBoxShow(text=f'Bạn chưa nhập Token TLC');return

    """Chức năng Frame Extension"""
    def get_folders(self, path):
        if os.path.exists(path) and os.path.isdir(path):
            folders = [folder for folder in os.listdir(path) if os.path.isdir(os.path.join(path, folder))]
            return folders
        else:
            return []
        
    def getPathIcon(self,path):
        try:
            for file in os.listdir(path):
                if file.lower().endswith('.png') or file.lower().endswith('.jpg') or file.lower().endswith('.jpeg'):
                    return os.path.join(path, file)  # Trả về đường dẫn tệp tin hình ảnh nếu tìm thấy trong thư mục này

            # Nếu không tìm thấy trong thư mục ban đầu, tiếp tục tìm trong các thư mục con
            for root, dirs, files in os.walk(path):
                for dir in dirs:
                    icon_folder_path = os.path.join(root, dir)
                    for icon_root, icon_dirs, icon_files in os.walk(icon_folder_path):
                        for file in icon_files:
                            if file.lower().endswith('.png') or file.lower().endswith('.jpg') or file.lower().endswith('.jpeg'):
                                return os.path.join(icon_root, file)  # Trả về đường dẫn tệp tin hình ảnh nếu tìm thấy trong thư mục con
        except:return ''

    def loadExtension(self):
        current_layout = self.frameExten1.layout()
        if current_layout:
            QWidget().setLayout(current_layout)

        # Tạo layout dọc cho frameExten1
        layout_frameExten1 = QVBoxLayout(self.frameExten1)
        layout_frameExten1.setContentsMargins(0, 0, 0, 0)
        layout_frameExten1.setSpacing(0)
        folder_in_extension = self.get_folders(self.pathExt)
        for i, name in enumerate(folder_in_extension,start=0):
           # Tạo QFrame và đặt vào layout dọc của frameExten1
            frame = QtWidgets.QFrame()
            frame.setObjectName(f"FrameExt_{name}")
            layout_frameExten1.addWidget(frame)

            # Tạo layout ngang cho từng QFrame
            layout_horizontal = QHBoxLayout(frame)
            layout_horizontal.setContentsMargins(3, 5, 6, 3)
            layout_horizontal.setSpacing(6)

            # Tạo QPushButton, QCheckBox và QPushButton và đặt vào layout ngang của từng QFrame
            nameExtBtn = QtWidgets.QPushButton(f"{name}")
            layout_horizontal.addWidget(nameExtBtn)
            nameExtBtn.setObjectName(f"nameExtBtn_{name}")
            iconpath = self.getPathIcon(f'{self.pathExt}/{name}')
            icon = QtGui.QIcon()
            icon.addPixmap(QtGui.QPixmap(iconpath), QtGui.QIcon.Normal, QtGui.QIcon.Off)
            nameExtBtn.setIcon(icon)
            nameExtBtn.setIconSize(QtCore.QSize(24, 24))

            layout_horizontal.addStretch()
            cboxExt = PyToggle()
            layout_horizontal.addWidget(cboxExt)
            cboxExt.setObjectName(f"cboxExt_{name}")
            cboxExt.clicked.connect(partial(self.saveClickedCbox, cboxExt))
            try:
                with open(f"{pathConfig}\\extension.json", "r", encoding='utf-8') as infile:
                    settings_data = json.load(infile)
            except Exception as e :settings_data = {}
            
            if 'ExtensionChecked' in settings_data:
                if name in str(settings_data['ExtensionChecked']):
                    cboxExt.setChecked(settings_data['ExtensionChecked'][name])
            removeExBtn = QtWidgets.QPushButton()
            layout_horizontal.addWidget(removeExBtn)
            removeExBtn.setObjectName(f"removeExBtn_{name}")
            iconpath = f'.\\images/images/delete_sign_25px.png'
            icon = QtGui.QIcon()
            icon.addPixmap(QtGui.QPixmap(iconpath), QtGui.QIcon.Normal, QtGui.QIcon.Off)
            removeExBtn.setIcon(icon)
            removeExBtn.setIconSize(QtCore.QSize(24, 24))
            removeExBtn.setStyleSheet('''
QPushButton{
border-radius:15px;
text-align:center;
width:10;
height:8;
padding-right:11;
padding-left:10;
padding-top:10;
padding-bottom:10;
}

QPushButton:hover {
	background-color:#343b48;
	border-radius:2px;

}


QPushButton:pressed { 
background-color: #ff5555;
 border-style: solid; border-radius: 2px; 
}


''')
            removeExBtn.clicked.connect(partial(self.removeExt, frame, cboxExt))

        layout_frameExten1.addStretch()
    
    def removeExt(self, frame, cbox):
        checkbox_name = cbox.objectName().split('_')[1]
        try:
            with open(f"{pathConfig}/extension.json", "r") as file:
                dataExtension = json.load(file)
        except FileNotFoundError:
            dataExtension = {}

        if "ExtensionChecked" in dataExtension and checkbox_name in dataExtension["ExtensionChecked"]:
    
            del dataExtension['ExtensionChecked'][checkbox_name]
            writeJson(f"{pathConfig}\\extension.json",dataExtension)
        nameExt = frame.objectName().split('_')[1]
        try:
            shutil.rmtree(f'{self.pathExt}//{nameExt}')
        except:self.uiFuncions.MessageBoxShow(text=f'Delete Extension {nameExt} Faild!!!')
        self.loadExtension()
        
    def saveClickedCbox(self, cbox):
        try:
            checkbox_name = cbox.objectName().split('_')[1]
            try:
                with open(f"{pathConfig}/extension.json", "r") as file:
                    dataExtension = json.load(file)
            except FileNotFoundError:
                dataExtension = {}

            if 'ExtensionChecked' not in dataExtension:
                dataExtension['ExtensionChecked'] = {}
            dataExtension['ExtensionChecked'].update({checkbox_name: cbox.isChecked()})
            writeJson(f"{pathConfig}\\extension.json",dataExtension)
        except:self.uiFuncions.MessageBoxShow(text=f'Save {cbox.objectName().split("_")[1]} Faild!!!')
        
    def uploadExt(self):
        try:
            filepath = self.uiFuncions.DialogFolder()
            new_destination = os.path.join(self.pathExt, os.path.basename(filepath))
            shutil.copytree(filepath, new_destination)
            self.loadExtension()
        except:
            if filepath != None:self.uiFuncions.MessageBoxShow(text=f'Upload Extension Faild!!!')

    """Chức năng di chuyển zoom in zoom out APP"""
    def updateGripPositions(self):
        self.grips[0].move(0, 0) 
        self.grips[1].move(self.APP.width() - self.gripSize, 0) 
        self.grips[2].move(0, self.APP.height() - self.gripSize)  
        self.grips[3].move(self.APP.width() - self.gripSize, self.APP.height() - self.gripSize)

    def resizeEvent(self, event):
        self.APP.resizeEvent(event)
        self.updateGripPositions()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = event.pos()
            
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._old_pos = None
            
    def mouseMoveEvent(self, event):
        if not self._old_pos:
            return
        delta = event.pos() - self._old_pos
        self.APP.move(self.APP.pos() + delta)

    def closeAPP(self):  
        self.APP.close()
