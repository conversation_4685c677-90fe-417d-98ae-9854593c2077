function blockRequest(details) {
    return { cancel: true };
}

chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    console.log(changeInfo);
    if (changeInfo.title && changeInfo.title.includes('PYTOURNES:BLOCK_OFF')) {
        chrome.webRequest.onBeforeRequest.removeListener(blockRequest);
    }
    if (changeInfo.title && changeInfo.title.includes('PYTOURNES:BLOCK_ON')) {
        print(changeInfo.title)
        chrome.webRequest.onBeforeRequest.addListener(
            blockRequest,
            { urls: ["<all_urls>"], types: ["image", "media"] },
            ["blocking"]
        );
    }
});
